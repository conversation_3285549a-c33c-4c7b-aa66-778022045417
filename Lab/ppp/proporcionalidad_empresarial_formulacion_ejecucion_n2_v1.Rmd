---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]

# Metadatos ICFES
icfes:
  competencia:
    - formulacion_ejecucion
  nivel_dificultad: 2
  contenido:
    categoria: algebra_calculo
    tipo: generico
  contexto: laboral
  eje_axial: eje3
  componente: numerico_variacional
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos para competencia FORMULACIÓN Y EJECUCIÓN
generar_datos <- function() {
  # Nombres aleatorios para los socios (diversidad de género)
  nombres_masculinos <- c("Mario", "Carlos", "Luis", "Pedro", "Juan", "Diego", "Andrés", "Miguel", "José", "Roberto", "Fernando", "Alejandro")
  nombres_femeninos <- c("Carolina", "Lucía", "María", "Ana", "Patricia", "Laura", "Carmen", "Rosa", "Elena", "Isabel", "Sofía", "Valentina")
  
  # Seleccionar 3 nombres (al menos uno de cada género)
  nombre1 <- sample(nombres_masculinos, 1)
  nombre2 <- sample(nombres_femeninos, 1)
  nombre3 <- sample(c(nombres_masculinos, nombres_femeninos), 1)
  
  # Asegurar que los 3 nombres sean diferentes
  while(nombre3 == nombre1 || nombre3 == nombre2) {
    nombre3 <- sample(c(nombres_masculinos, nombres_femeninos), 1)
  }
  
  nombres <- c(nombre1, nombre2, nombre3)
  
  # Generar cantidades de acciones que sumen un número fácil de manejar
  # Usar múltiplos que faciliten los cálculos
  total_acciones_opciones <- c(20, 24, 30, 36, 40, 48, 50, 60)
  total_acciones <- sample(total_acciones_opciones, 1)
  
  # Generar distribución de acciones
  # Asegurar que cada socio tenga al menos 2 acciones
  min_acciones <- 2
  acciones_restantes <- total_acciones - (3 * min_acciones)
  
  # Distribuir las acciones restantes aleatoriamente
  distribucion <- sample(0:acciones_restantes, 2)
  acciones1 <- min_acciones + distribucion[1]
  acciones2 <- min_acciones + distribucion[2]
  acciones3 <- total_acciones - acciones1 - acciones2
  
  # Asegurar que acciones3 sea positivo
  if(acciones3 < min_acciones) {
    # Redistribuir
    acciones1 <- min_acciones + sample(0:(acciones_restantes-min_acciones), 1)
    acciones3 <- min_acciones + sample(0:(acciones_restantes-min_acciones), 1)
    acciones2 <- total_acciones - acciones1 - acciones3
  }
  
  acciones <- c(acciones1, acciones2, acciones3)
  
  # Generar ganancias totales (en millones)
  ganancias_opciones <- c(60, 80, 100, 120, 150, 180, 200, 240, 300, 360, 400, 480, 500, 600)
  ganancias_total <- sample(ganancias_opciones, 1)
  
  # Calcular la ganancia correcta para el primer socio
  ganancia_correcta <- (acciones[1] / total_acciones) * ganancias_total
  
  # Generar procedimientos aleatorios
  procedimientos <- list(
    correcto = list(
      texto = paste0("Dividir las ganancias en ", total_acciones, " partes y, luego, multiplicar ese valor por ", acciones[1]),
      calculo = ganancia_correcta
    ),
    incorrecto1 = list(
      texto = paste0("Multiplicar las ganancias por ", acciones[1], " y, luego, dividir ese resultado en ", total_acciones * 10),
      calculo = (ganancias_total * acciones[1]) / (total_acciones * 10)
    ),
    incorrecto2 = list(
      texto = paste0("Dividir las ganancias por ", acciones[1], " y, luego, multiplicar por ", total_acciones),
      calculo = (ganancias_total / acciones[1]) * total_acciones
    ),
    incorrecto3 = list(
      texto = paste0("Multiplicar las ganancias por ", total_acciones, " y, luego, dividir por ", acciones[1] * 100),
      calculo = (ganancias_total * total_acciones) / (acciones[1] * 100)
    )
  )
  
  # Seleccionar dos procedimientos aleatoriamente (uno correcto, uno incorrecto)
  proc_incorrecto <- sample(c("incorrecto1", "incorrecto2", "incorrecto3"), 1)
  
  # Decidir el orden de presentación
  orden <- sample(c(TRUE, FALSE), 1)
  if(orden) {
    procedimiento1 <- procedimientos$correcto
    procedimiento2 <- procedimientos[[proc_incorrecto]]
    respuesta_correcta <- "Solo el procedimiento 1 es correcto"
  } else {
    procedimiento1 <- procedimientos[[proc_incorrecto]]
    procedimiento2 <- procedimientos$correcto
    respuesta_correcta <- "Solo el procedimiento 2 es correcto"
  }
  
  return(list(
    nombres = nombres,
    acciones = acciones,
    total_acciones = total_acciones,
    ganancias_total = ganancias_total,
    ganancia_correcta = ganancia_correcta,
    procedimiento1 = procedimiento1,
    procedimiento2 = procedimiento2,
    respuesta_correcta = respuesta_correcta
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
nombres <- datos$nombres
acciones <- datos$acciones
total_acciones <- datos$total_acciones
ganancias_total <- datos$ganancias_total
ganancia_correcta <- datos$ganancia_correcta
procedimiento1 <- datos$procedimiento1
procedimiento2 <- datos$procedimiento2
respuesta_correcta <- datos$respuesta_correcta
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba de diversidad de versiones para competencia FORMULACIÓN Y EJECUCIÓN
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})

test_that("Prueba de coherencia matemática", {
  for(i in 1:20) {
    datos_test <- generar_datos()

    # Verificar que las acciones sumen el total
    expect_equal(sum(datos_test$acciones), datos_test$total_acciones)

    # Verificar que la ganancia correcta sea proporcional
    proporcion_esperada <- datos_test$acciones[1] / datos_test$total_acciones
    ganancia_esperada <- proporcion_esperada * datos_test$ganancias_total
    expect_equal(datos_test$ganancia_correcta, ganancia_esperada, tolerance = 0.01)

    # Verificar que todos los socios tengan al menos 2 acciones
    expect_true(all(datos_test$acciones >= 2))
  }
})
```

```{r generar_graficos_python, echo=FALSE, results="hide"}
# Crear gráfico de distribución de acciones usando Python
codigo_python <- paste0('
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import numpy as np
import random

# Recibir datos desde R
nombres_r = r.nombres
acciones_r = r.acciones
ganancias_total_r = r.ganancias_total

# Configuración de colores aleatorios
colores_disponibles = ["#4285F4", "#EA4335", "#FBBC05", "#34A853", "#9C27B0", "#FF9800"]
colores_seleccionados = random.sample(colores_disponibles, 3)

# Crear figura con subplots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Gráfico de barras - Distribución de acciones
ax1.bar(nombres_r, acciones_r, color=colores_seleccionados)
ax1.set_title("Distribución de Acciones por Socio", fontsize=14, fontweight="bold")
ax1.set_xlabel("Socios", fontsize=12)
ax1.set_ylabel("Número de Acciones", fontsize=12)
ax1.grid(axis="y", alpha=0.3)

# Agregar valores en las barras
for i, v in enumerate(acciones_r):
    ax1.text(i, v + 0.5, str(int(v)), ha="center", va="bottom", fontweight="bold")

# Gráfico circular - Proporción de acciones
total_acciones = sum(acciones_r)
proporciones = [acc/total_acciones for acc in acciones_r]
ax2.pie(proporciones, labels=nombres_r, colors=colores_seleccionados, autopct="%1.1f%%", startangle=90)
ax2.set_title("Proporción de Acciones", fontsize=14, fontweight="bold")

plt.tight_layout()
plt.savefig("distribucion_acciones.png", dpi=150, bbox_inches="tight")
plt.close()
')

# Ejecutar código Python para generar la figura
py_run_string(codigo_python)
```

Question
========

`r nombres[1]`, `r nombres[2]` y `r nombres[3]` son los únicos dueños de una empresa. `r nombres[1]` tiene `r acciones[1]` acciones; `r nombres[2]`, `r acciones[2]`; y `r nombres[3]`, `r acciones[3]`. El año pasado, las ganancias fueron de $`r format(ganancias_total * 1000000, big.mark=".", decimal.mark=",")` y ellos quieren repartirlas proporcionalmente, de modo que, a quien más tenga acciones, le correspondan más ganancias. Para saber qué parte del dinero le correspondió a `r nombres[1]`, este plantea dos procedimientos:

**Procedimiento 1.** `r procedimiento1$texto`.

**Procedimiento 2.** `r procedimiento2$texto`.

```{r tabla_distribucion, echo=FALSE, results='asis', fig.align='center'}
# Detectar si se está generando para Moodle u otros formatos
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# Incluir la imagen generada por Python
if (es_moodle) {
  # Tamaño para Moodle
  cat("![](distribucion_acciones.png){width=80%}")
} else {
  # Tamaño para PDF/Word
  cat("![](distribucion_acciones.png){width=90%}")
}
```

¿Cuál o cuáles de los procedimientos es o son correctos?

Answerlist
----------
- Ambos procedimientos son correctos.
- Solo el procedimiento 1 es correcto.
- Solo el procedimiento 2 es correcto.
- Ninguno de los procedimientos es correcto.

Solution
========

Para resolver este problema de reparto proporcional, debemos analizar cada procedimiento y verificar si calcula correctamente la parte que le corresponde a `r nombres[1]`.

### Datos del problema:
- `r nombres[1]`: `r acciones[1]` acciones
- `r nombres[2]`: `r acciones[2]` acciones
- `r nombres[3]`: `r acciones[3]` acciones
- **Total de acciones**: `r total_acciones` acciones
- **Ganancias totales**: $`r format(ganancias_total * 1000000, big.mark=".", decimal.mark=",")`

### Método correcto para el reparto proporcional:

El reparto debe ser proporcional al número de acciones que posee cada socio. La fórmula correcta es:

**Ganancia de cada socio = (Número de acciones del socio ÷ Total de acciones) × Ganancias totales**

Para `r nombres[1]`:
Ganancia = (`r acciones[1]` ÷ `r total_acciones`) × $`r format(ganancias_total * 1000000, big.mark=".", decimal.mark=",")`

Esto es equivalente a:
1. Dividir las ganancias totales entre el número total de acciones para obtener el valor por acción
2. Multiplicar ese valor por el número de acciones de `r nombres[1]`

**Valor por acción** = $`r format(ganancias_total * 1000000, big.mark=".", decimal.mark=",")` ÷ `r total_acciones` = $`r format((ganancias_total * 1000000) / total_acciones, big.mark=".", decimal.mark=",")`

**Ganancia de `r nombres[1]`** = $`r format((ganancias_total * 1000000) / total_acciones, big.mark=".", decimal.mark=",")` × `r acciones[1]` = $`r format(ganancia_correcta * 1000000, big.mark=".", decimal.mark=",")`

### Análisis de los procedimientos:

**Procedimiento 1:** `r procedimiento1$texto`
- Resultado: $`r format(procedimiento1$calculo * 1000000, big.mark=".", decimal.mark=",")`
- `r if(abs(procedimiento1$calculo - ganancia_correcta) < 0.01) "✓ CORRECTO" else "✗ INCORRECTO"`

**Procedimiento 2:** `r procedimiento2$texto`
- Resultado: $`r format(procedimiento2$calculo * 1000000, big.mark=".", decimal.mark=",")`
- `r if(abs(procedimiento2$calculo - ganancia_correcta) < 0.01) "✓ CORRECTO" else "✗ INCORRECTO"`

### Verificación:
Comprobemos que el reparto es proporcional:
- `r nombres[1]` (`r acciones[1]` acciones): $`r format(ganancia_correcta * 1000000, big.mark=".", decimal.mark=",")` = `r round((acciones[1]/total_acciones)*100, 1)`% del total
- `r nombres[2]` (`r acciones[2]` acciones): $`r format(((acciones[2]/total_acciones) * ganancias_total) * 1000000, big.mark=".", decimal.mark=",")` = `r round((acciones[2]/total_acciones)*100, 1)`% del total
- `r nombres[3]` (`r acciones[3]` acciones): $`r format(((acciones[3]/total_acciones) * ganancias_total) * 1000000, big.mark=".", decimal.mark=",")` = `r round((acciones[3]/total_acciones)*100, 1)`% del total

**Total verificado**: $`r format(ganancias_total * 1000000, big.mark=".", decimal.mark=",")` ✓

Por lo tanto, **`r respuesta_correcta`**.

Answerlist
----------
- `r if(respuesta_correcta == "Ambos procedimientos son correctos") "Verdadero" else "Falso"`
- `r if(respuesta_correcta == "Solo el procedimiento 1 es correcto") "Verdadero" else "Falso"`
- `r if(respuesta_correcta == "Solo el procedimiento 2 es correcto") "Verdadero" else "Falso"`
- `r if(respuesta_correcta == "Ninguno de los procedimientos es correcto") "Verdadero" else "Falso"`

Meta-information
================
exname: proporcionalidad_empresarial_formulacion_ejecucion
extype: schoice
exsolution: `r paste(as.integer(c(respuesta_correcta == "Ambos procedimientos son correctos", respuesta_correcta == "Solo el procedimiento 1 es correcto", respuesta_correcta == "Solo el procedimiento 2 es correcto", respuesta_correcta == "Ninguno de los procedimientos es correcto")), collapse="")`
exshuffle: TRUE
exsection: Álgebra y Cálculo|Proporcionalidad|Reparto proporcional
